/**
 * 将选中的div导出为PNG图片的浏览器控制台脚本
 * 支持包含SVG的复杂DOM结构，并可设置半透明效果
 */

async function exportSelectedDivToPNG() {
    // 检查是否有选中的元素
    const selection = window.getSelection();
    let targetElement = null;
    
    if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        targetElement = range.commonAncestorContainer;
        
        // 如果选中的是文本节点，获取其父元素
        if (targetElement.nodeType === Node.TEXT_NODE) {
            targetElement = targetElement.parentElement;
        }
        
        // 确保选中的是div元素
        while (targetElement && targetElement.tagName !== 'DIV') {
            targetElement = targetElement.parentElement;
        }
    }
    
    // 如果没有选中div，提示用户
    if (!targetElement) {
        alert('请先选中一个div元素！\n操作方法：用鼠标拖拽选择div内的内容，然后运行此脚本。');
        return;
    }
    
    console.log('找到目标div元素：', targetElement);
    
    try {
        // 动态加载html2canvas库（如果未加载）
        if (typeof html2canvas === 'undefined') {
            console.log('正在加载html2canvas库...');
            await loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js');
        }
        
        // 获取元素的样式和尺寸
        const rect = targetElement.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(targetElement);
        
        console.log('元素尺寸：', rect.width, 'x', rect.height);
        
        // 配置html2canvas选项
        const options = {
            // 基本配置
            allowTaint: true,
            useCORS: true,
            scale: 2, // 提高清晰度
            
            // 尺寸配置
            width: rect.width,
            height: rect.height,
            
            // 背景配置 - 设置为透明
            backgroundColor: null,
            
            // SVG支持
            foreignObjectRendering: true,
            
            // 其他配置
            logging: true,
            removeContainer: true,
            
            // 忽略某些元素（可选）
            ignoreElements: function(element) {
                // 可以在这里添加需要忽略的元素
                return false;
            }
        };
        
        console.log('开始渲染canvas...');
        
        // 使用html2canvas渲染元素
        const canvas = await html2canvas(targetElement, options);
        
        // 创建一个新的canvas来处理半透明效果
        const finalCanvas = document.createElement('canvas');
        const ctx = finalCanvas.getContext('2d');
        
        finalCanvas.width = canvas.width;
        finalCanvas.height = canvas.height;
        
        // 设置全局透明度（0.8表示80%不透明度，可以调整）
        ctx.globalAlpha = 0.8;
        
        // 绘制原始图像
        ctx.drawImage(canvas, 0, 0);
        
        // 转换为PNG数据URL
        const dataURL = finalCanvas.toDataURL('image/png');
        
        // 创建下载链接
        const link = document.createElement('a');
        link.download = `div-export-${Date.now()}.png`;
        link.href = dataURL;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('PNG图片导出成功！');
        
        // 可选：在新窗口中预览图片
        const previewWindow = window.open();
        previewWindow.document.write(`
            <html>
                <head><title>导出预览</title></head>
                <body style="margin:0; padding:20px; background:#f0f0f0;">
                    <h3>导出的PNG图片预览：</h3>
                    <img src="${dataURL}" style="max-width:100%; border:1px solid #ccc; box-shadow:0 2px 10px rgba(0,0,0,0.1);">
                </body>
            </html>
        `);
        
    } catch (error) {
        console.error('导出失败：', error);
        alert('导出失败：' + error.message);
    }
}

// 辅助函数：动态加载脚本
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 增强版本：支持手动选择元素
function exportDivToPNGAdvanced() {
    console.log('进入高级选择模式...');
    console.log('请点击要导出的div元素');
    
    // 创建选择模式
    let isSelecting = true;
    const originalCursor = document.body.style.cursor;
    document.body.style.cursor = 'crosshair';
    
    // 高亮样式
    const highlightStyle = 'outline: 3px solid #ff4444; outline-offset: 2px;';
    let currentHighlighted = null;
    
    function highlightElement(element) {
        if (currentHighlighted) {
            currentHighlighted.style.cssText = currentHighlighted.style.cssText.replace(highlightStyle, '');
        }
        if (element && element.tagName === 'DIV') {
            element.style.cssText += highlightStyle;
            currentHighlighted = element;
        }
    }
    
    function onMouseOver(e) {
        if (!isSelecting) return;
        e.stopPropagation();
        highlightElement(e.target);
    }
    
    function onMouseClick(e) {
        if (!isSelecting) return;
        e.preventDefault();
        e.stopPropagation();
        
        if (e.target.tagName === 'DIV') {
            isSelecting = false;
            document.body.style.cursor = originalCursor;
            
            // 移除事件监听器
            document.removeEventListener('mouseover', onMouseOver, true);
            document.removeEventListener('click', onMouseClick, true);
            
            // 移除高亮
            if (currentHighlighted) {
                currentHighlighted.style.cssText = currentHighlighted.style.cssText.replace(highlightStyle, '');
            }
            
            console.log('选中的div：', e.target);
            
            // 导出选中的div
            exportSpecificDiv(e.target);
        } else {
            alert('请点击一个div元素！');
        }
    }
    
    // 添加事件监听器
    document.addEventListener('mouseover', onMouseOver, true);
    document.addEventListener('click', onMouseClick, true);
    
    // 5秒后自动取消选择模式
    setTimeout(() => {
        if (isSelecting) {
            isSelecting = false;
            document.body.style.cursor = originalCursor;
            document.removeEventListener('mouseover', onMouseOver, true);
            document.removeEventListener('click', onMouseClick, true);
            if (currentHighlighted) {
                currentHighlighted.style.cssText = currentHighlighted.style.cssText.replace(highlightStyle, '');
            }
            console.log('选择模式已超时取消');
        }
    }, 5000);
}

// 导出指定div的函数
async function exportSpecificDiv(targetElement) {
    try {
        // 动态加载html2canvas库（如果未加载）
        if (typeof html2canvas === 'undefined') {
            console.log('正在加载html2canvas库...');
            await loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js');
        }
        
        const rect = targetElement.getBoundingClientRect();
        console.log('开始导出div，尺寸：', rect.width, 'x', rect.height);
        
        const canvas = await html2canvas(targetElement, {
            allowTaint: true,
            useCORS: true,
            scale: 2,
            backgroundColor: null, // 透明背景
            foreignObjectRendering: true,
            logging: false
        });
        
        // 应用半透明效果
        const finalCanvas = document.createElement('canvas');
        const ctx = finalCanvas.getContext('2d');
        finalCanvas.width = canvas.width;
        finalCanvas.height = canvas.height;
        
        ctx.globalAlpha = 0.8; // 80%不透明度
        ctx.drawImage(canvas, 0, 0);
        
        // 下载图片
        const dataURL = finalCanvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `div-export-${Date.now()}.png`;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('导出成功！');
        
    } catch (error) {
        console.error('导出失败：', error);
        alert('导出失败：' + error.message);
    }
}

// 使用说明
console.log(`
=== DIV导出为PNG工具 ===

使用方法1（推荐）：
exportDivToPNGAdvanced()  // 进入点击选择模式

使用方法2：
1. 先用鼠标选中目标div中的内容
2. 然后执行：exportSelectedDivToPNG()

使用方法3：
exportSpecificDiv(document.querySelector('#your-div-id'))  // 直接指定元素

特性：
- 支持包含SVG的复杂DOM结构
- 自动设置半透明效果（80%不透明度）
- 高清晰度导出（2x缩放）
- 透明背景支持
`);

// 快速执行函数
window.exportDiv = exportDivToPNGAdvanced;
